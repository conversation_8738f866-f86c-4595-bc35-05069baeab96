# API 文档

## 📚 核心类和方法

### reserve 类

主要的预约和签到功能类，位于 `utils/reserve.py`。

#### 初始化
```python
reserve(sleep_time=0.5, max_attempt=3, enable_slider=False, reserve_next_day=False)
```

**参数说明：**
- `sleep_time` (float): 请求间隔时间，默认0.5秒
- `max_attempt` (int): 最大重试次数，默认3次
- `enable_slider` (bool): 是否启用滑块验证，默认False
- `reserve_next_day` (bool): 是否预约次日，默认False

#### 核心方法

##### 1. login(username, password)
用户登录方法

**参数：**
- `username` (str): 用户名
- `password` (str): 密码

**返回值：**
- `tuple`: (登录状态, 消息)
  - `bool`: True表示登录成功，False表示失败
  - `str`: 登录结果消息

**示例：**
```python
s = reserve()
success, msg = s.login("username", "password")
if success:
    print("登录成功")
else:
    print(f"登录失败: {msg}")
```

##### 2. get_my_seat_id(seatId, seatIdEnc=None)
获取预约信息

**参数：**
- `seatId` (str/int): 座位ID
- `seatIdEnc` (str, 可选): 加密的座位ID，如不提供会自动获取

**返回值：**
- `list`: 预约信息列表，每个元素包含预约详情

**示例：**
```python
reservations = s.get_my_seat_id(seatId=602)
for reservation in reservations:
    print(f"预约ID: {reservation['id']}")
```

##### 3. sign_latest_reservation(seatId)
签到最近一次预约（新增功能）

**参数：**
- `seatId` (str/int): 座位ID

**返回值：**
- `str`: 签到结果消息

**示例：**
```python
result = s.sign_latest_reservation(seatId=602)
print(f"签到结果: {result}")
```

##### 4. sign(reservation_or_seatId)
通用签到方法

**参数：**
- `reservation_or_seatId` (dict/str/int): 预约对象或座位ID

**返回值：**
- `str`: 签到结果消息

**示例：**
```python
# 使用座位ID签到
result = s.sign(seatId=602)

# 使用预约对象签到
reservation = {"id": 123, "seatId": 602}
result = s.sign(reservation)
```

##### 5. get_seatidenc_from_html(seatId)
从HTML页面提取seatIdEnc参数

**参数：**
- `seatId` (str/int): 座位ID

**返回值：**
- `str`: 提取的seatIdEnc值，失败返回None

**示例：**
```python
seat_id_enc = s.get_seatidenc_from_html(602)
if seat_id_enc:
    print(f"提取的seatIdEnc: {seat_id_enc}")
```

## 🔧 API接口说明

### 1. 新版API接口

**URL:** `https://office.chaoxing.com/data/apps/seat/reservelist`

**方法:** GET

**参数:**
- `indexId`: 索引ID，通常为0
- `pageSize`: 页面大小，通常为100
- `type`: 预约类型，-1表示正常预约

**响应格式:**
```json
{
  "success": true,
  "data": {
    "reserveList": [
      {
        "id": 34730923,
        "seatId": 602,
        "startTime": 1751774400000,
        "endTime": 1751814000000,
        "status": 1,
        "type": -1
      }
    ],
    "showQrCode": 1,
    "exitNextPage": false
  }
}
```

### 2. 老版API接口

**URL:** `https://office.chaoxing.com/data/apps/seatengine/reservelist`

**方法:** GET

**参数:**
- `cpage`: 当前页，从1开始
- `pageSize`: 页面大小，通常为10
- `type`: 预约类型，-1表示正常预约
- `seatId`: 座位ID
- `seatIdEnc`: 加密的座位ID
- `showQrCode`: 是否显示二维码，通常为1

**响应格式:**
```json
{
  "success": true,
  "data": {
    "reserveList": [
      {
        "id": 34730923,
        "seatId": 602,
        "seatNum": "114",
        "startTime": 1751774400000,
        "endTime": 1751814000000,
        "status": 1,
        "type": -1,
        "firstLevelName": "南区分馆",
        "secondLevelName": "四楼",
        "thirdLevelName": ""
      }
    ],
    "totalRow": 358,
    "totalPage": 36,
    "showQrCode": 1
  }
}
```

### 3. 签到API接口

**URL:** `https://office.chaoxing.com/front/third/apps/seatengine/sign`

**方法:** POST

**参数:**
- `id`: 预约ID
- `seatId`: 座位ID
- `seatIdEnc`: 加密的座位ID

**响应格式:**
```json
{
  "success": true,
  "message": "签到成功"
}
```

## 📊 状态码说明

### 预约状态 (status)
- `0`: 待履约
- `1`: 学习中（已签到）
- `2`: 已履约（已完成）
- `3`: 暂离中
- `5`: 被监督中
- `7`: 已取消

### 预约类型 (type)
- `-1`: 正常预约
- `0`: 待履约预约

### 签到状态常量
```python
# 在 reserve 类中定义的常量
SIGNED_STATUS = 1           # 已签到状态
SIGNABLE_STATUS = [0]       # 可签到状态列表
```

## 🔍 错误处理

### 常见错误码
- `HTTP 403`: 权限不足，通常是请求头不完整
- `HTTP 404`: 接口不存在或参数错误
- `HTTP 500`: 服务器内部错误

### 异常处理示例
```python
try:
    result = s.sign_latest_reservation(seatId=602)
    print(f"签到成功: {result}")
except Exception as e:
    print(f"签到失败: {e}")
    # 记录详细错误信息
    import traceback
    traceback.print_exc()
```

## 🛠️ 工具函数

### 时间转换
```python
import datetime

def timestamp_to_datetime(timestamp):
    """将时间戳转换为可读时间"""
    return datetime.datetime.fromtimestamp(timestamp / 1000)

# 使用示例
start_time = timestamp_to_datetime(1751774400000)
print(start_time.strftime("%Y-%m-%d %H:%M:%S"))
```

### 状态描述
```python
def get_status_description(status):
    """获取状态描述"""
    status_map = {
        0: "待履约",
        1: "学习中", 
        2: "已履约",
        3: "暂离中",
        5: "被监督中",
        7: "已取消"
    }
    return status_map.get(status, f"未知状态({status})")
```

### 预约类型描述
```python
def get_type_description(type_code):
    """获取预约类型描述"""
    type_map = {
        -1: "正常预约",
        0: "待履约预约"
    }
    return type_map.get(type_code, f"未知类型({type_code})")
```

## 📝 日志配置

### 启用详细日志
```python
import logging

# 配置日志格式
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
```

### 日志级别说明
- `DEBUG`: 详细的调试信息，包括API请求和响应
- `INFO`: 一般信息，如登录成功、签到结果
- `WARNING`: 警告信息，如API切换、参数缺失
- `ERROR`: 错误信息，如登录失败、网络错误

## 🔐 安全注意事项

1. **密码保护**: 配置文件中的密码会被加密存储
2. **请求头**: 必须设置完整的请求头以避免被拦截
3. **频率限制**: 避免过于频繁的API请求
4. **会话管理**: 登录会话有时效性，需要定期重新登录

---

**注意**: 此API文档基于当前版本的实现，如有更新请及时同步修改。
