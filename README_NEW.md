# 学习通签到系统 (老版本)

这是一个用于学习通平台的自动签到系统，专门针对老版本的学习通预约系统设计。

## 🎯 功能特点

### 核心功能
- ✅ **自动登录**：支持学习通账号自动登录
- ✅ **智能预约获取**：自动获取用户的预约信息
- ✅ **最近预约签到**：只显示最近一次预约，简化用户体验
- ✅ **多账号支持**：支持批量管理多个账号
- ✅ **跨平台兼容**：支持不同座位系统的动态参数提取

### 技术特点
- 🔄 **双API系统**：新版API + 老版API智能切换
- 🔐 **动态参数提取**：自动从HTML中提取 `seatIdEnc` 参数
- 📊 **详细状态显示**：清晰的预约信息和签到状态
- 🛡️ **错误处理完善**：各种异常情况都有适当处理
- 📝 **日志记录完整**：详细的调试和运行日志

## 🚀 快速开始

### 1. 环境准备
```bash
# 确保已安装 Python 3.7+
python --version

# 安装依赖包
pip install requests cryptography
```

### 2. 配置账号
编辑 `config2.json` 文件：
```json
{
  "reserve": [
    {
      "username": "你的用户名",
      "password": "你的密码",
      "seatId": "602",
      "comment": "账号描述"
    }
  ]
}
```

### 3. 运行签到
```bash
# 进入项目目录
cd xuexitong_old

# 运行主程序
python main.py
```

## 📋 详细使用说明

### 配置文件说明
- `username`: 学习通登录用户名
- `password`: 学习通登录密码  
- `seatId`: 座位系统ID（如：602, 2793等）
- `comment`: 账号备注，便于识别

### 签到流程
1. **登录验证**：自动登录学习通账号
2. **获取预约**：从新版和老版API获取预约列表
3. **选择最近预约**：自动选择开始时间最近的预约
4. **显示详情**：展示预约ID、类型、状态、座位、位置、时间
5. **执行签到**：根据预约状态执行相应操作

### 签到状态说明
- ✅ **已签到**：预约已经签到成功，显示学习提示
- 🔄 **开始签到**：预约符合签到条件，开始执行签到
- ❌ **无法签到**：预约状态不允许签到（如已取消、已过期等）
- 📋 **无预约信息**：当前没有找到有效的预约记录

## 🔧 高级配置

### 主程序参数
在 `main.py` 中可以调整以下参数：
- `SLEEPTIME`: 请求间隔时间
- `MAX_ATTEMPT`: 最大重试次数
- `ENABLE_SLIDER`: 是否启用滑块验证
- `RESERVE_NEXT_DAY`: 是否预约次日

### API配置
系统会自动处理以下API：
- **新版API**: `/data/apps/seat/reservelist`
- **老版API**: `/data/apps/seatengine/reservelist`
- **参数提取**: 从HTML页面动态提取 `seatIdEnc`

## 🐛 故障排除

### 常见问题

**1. 登录失败**
- 检查用户名和密码是否正确
- 确认网络连接正常
- 查看是否需要验证码

**2. 无预约信息**
- 确认 `seatId` 参数正确
- 检查是否有有效的预约记录
- 验证预约日期是否为今天或明天

**3. 签到失败**
- 检查签到时间窗口（通常为预约开始前后15分钟）
- 确认预约状态是否允许签到
- 查看详细日志信息

### 调试模式
系统默认只显示警告和错误信息，保持界面简洁。如需查看详细调试信息：

**临时启用调试模式**：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

**修改主程序日志级别**：
在 `main.py` 第6行修改：
```python
# 简洁模式（默认）- 只显示警告和错误
logging.basicConfig(level=logging.WARNING, format='%(asctime)s - %(levelname)s - %(message)s')

# 详细模式 - 显示所有信息（调试用）
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
```

## 📁 项目结构

```
xuexitong_old/
├── main.py                 # 主程序入口
├── utils/
│   ├── reserve.py         # 核心预约和签到功能
│   ├── encrypt.py         # 加密相关功能
│   └── check.py          # 检查和验证功能
├── config2.json          # 配置文件
├── logs/                 # 日志目录
└── README_NEW.md        # 说明文档
```

## 🔄 更新日志

### v2.0 (2025-07-06)
- ✅ 新增 `sign_latest_reservation()` 方法
- ✅ 实现最近预约自动选择和签到
- ✅ 优化用户界面，使用图标和格式化输出
- ✅ 完善错误处理和状态判断
- ✅ 集成到主程序，支持批量操作

### v1.5 (2025-07-05)
- ✅ 实现动态 `seatIdEnc` 参数提取
- ✅ 添加双API系统支持
- ✅ 优化HTML解析和参数获取
- ✅ 完善跨账号兼容性

## ⚠️ 注意事项

1. **时间窗口**：签到通常只能在预约开始前后15分钟内进行
2. **账号安全**：请妥善保管账号信息，不要泄露给他人
3. **网络环境**：确保网络连接稳定，避免请求失败
4. **合规使用**：请遵守学习通平台的使用规则和政策
5. **定期更新**：如遇到API变更，请及时更新代码

## 📞 技术支持

如遇到问题，请：
1. 查看日志文件获取详细错误信息
2. 检查配置文件格式是否正确
3. 确认网络和账号状态
4. 参考故障排除部分的解决方案

---

**免责声明**：本工具仅供学习和研究使用，请遵守相关平台的使用条款。
