#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试预约选择逻辑的修复
"""

def test_reservation_selection():
    """测试预约选择逻辑"""
    
    # 模拟多个预约数据 (使用正确的时间戳)
    from datetime import datetime

    # 2025-08-01 09:00:00 的时间戳
    morning_dt = datetime(2025, 8, 1, 9, 0, 0)
    morning_timestamp = int(morning_dt.timestamp() * 1000)

    # 2025-08-01 14:00:00 的时间戳
    afternoon_dt = datetime(2025, 8, 1, 14, 0, 0)
    afternoon_timestamp = int(afternoon_dt.timestamp() * 1000)

    mock_reservations = [
        {
            'id': 1,
            'startTime': morning_timestamp,  # 上午9:00
            'status': 0,  # 待履约
            'seatNum': '161',
            'location': '上午预约'
        },
        {
            'id': 2,
            'startTime': afternoon_timestamp,  # 下午14:00
            'status': 0,  # 待履约
            'seatNum': '162',
            'location': '下午预约'
        }
    ]
    
    print("🧪 测试预约选择逻辑")
    print("=" * 50)
    
    # 显示所有预约
    print("📋 可用预约:")
    for i, reservation in enumerate(mock_reservations):
        from datetime import datetime
        start_dt = datetime.fromtimestamp(reservation['startTime'] / 1000)
        print(f"  预约{i+1}: {reservation['location']} - {start_dt.strftime('%H:%M:%S')}")
    
    print("\n🔍 测试选择逻辑:")
    
    # 旧逻辑 (错误的)
    old_logic_result = max(mock_reservations, key=lambda x: int(x.get('startTime', 0)))
    old_start_dt = datetime.fromtimestamp(old_logic_result['startTime'] / 1000)
    print(f"❌ 旧逻辑 (max): 选择 {old_logic_result['location']} - {old_start_dt.strftime('%H:%M:%S')}")
    
    # 新逻辑 (正确的)
    new_logic_result = min(mock_reservations, key=lambda x: int(x.get('startTime', 0)))
    new_start_dt = datetime.fromtimestamp(new_logic_result['startTime'] / 1000)
    print(f"✅ 新逻辑 (min): 选择 {new_logic_result['location']} - {new_start_dt.strftime('%H:%M:%S')}")
    
    print("\n📊 结论:")
    print("- 旧逻辑会选择下午14:00的预约，可能错过上午9:00的签到时间")
    print("- 新逻辑会选择上午9:00的预约，确保用户能及时签到")
    print("- 修复成功！✅")

if __name__ == "__main__":
    test_reservation_selection()
