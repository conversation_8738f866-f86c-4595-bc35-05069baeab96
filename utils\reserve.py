from utils import AES_Encrypt, check, enc, generate_captcha_key
import json
import requests
import re
import time
import logging
import datetime
from urllib3.exceptions import InsecureRequestWarning

def wait_until_specific_time(target_time, reserve_next_day=False):
    target_hour, target_minute, target_second = map(int, target_time.split(':'))
    now = datetime.datetime.now()
    current_seconds = now.hour * 3600 + now.minute * 60 + now.second
    target_seconds = target_hour * 3600 + target_minute * 60 + target_second
    
    if target_seconds < current_seconds and reserve_next_day:
        target_seconds += 24 * 3600
    elif target_seconds < current_seconds:
        logging.info(f"目标时间 {target_time} 已过，立即执行")
        return
    
    seconds_to_wait = target_seconds - current_seconds
    
    if seconds_to_wait > 10:
        minutes_to_wait = seconds_to_wait // 60
        seconds_remainder = seconds_to_wait % 60
        logging.info(f"需要等待 {minutes_to_wait} 分 {seconds_remainder} 秒")
        time.sleep(seconds_to_wait - 5)
        while datetime.datetime.now().hour != target_hour or datetime.datetime.now().minute != target_minute or datetime.datetime.now().second != target_second:
            time.sleep(0.1)
    else:
        time.sleep(0.2)
    return

def get_date(day_offset: int=0):
    today = datetime.datetime.now().date()
    offset_day = today + datetime.timedelta(days=day_offset)
    tomorrow = offset_day.strftime("%Y-%m-%d")
    return tomorrow

class reserve:
    def __init__(self, sleep_time=0.2, max_attempt=50, enable_slider=False, reserve_next_day=False):
        self.login_page = "https://passport2.chaoxing.com/mlogin?loginType=1&newversion=true&fid="
        # self.url = "https://office.chaoxing.com/front/third/apps/seat/code?id={}&seatNum={}"
        self.url="https://reserve.chaoxing.com/front/apps/seatengine/select?id={}&backLevel=2&seatId={}"
        # self.url="https://reserve.chaoxing.com/front/apps/seatengine/select?id={}&backLevel=2"
        self.submit_url = "https://reserve.chaoxing.com/data/apps/seatengine/submit"
        self.seat_url = "https://office.chaoxing.com/data/apps/seat/getusedtimes"
        self.login_url = "https://passport2.chaoxing.com/fanyalogin"
        self.token = ""
        self.success_times = 0
        self.fail_dict = []
        self.submit_msg = []
        self.requests = requests.session()

        # 座位状态映射 (version=0 旧版系统)
        self.status = {
            '0': '待履约',
            '1': '学习中',
            '2': '已履约',
            '3': '暂离中',
            '5': '被监督中',
            '7': '已取消',
        }

        # 可签到的状态列表
        self.SIGNABLE_STATUS = [0, 3, 5]  # 待履约、暂离中、被监督中
        self.SIGNED_STATUS = 1  # 学习中（已签到）

        # 今天和明天的日期
        self.today = get_date(0)
        self.tomorrow = get_date(1)
        self.token_pattern = re.compile("token = '(.*?)'")
        self.headers = {
            "Referer": "https://office.chaoxing.com/",
            "Host": "captcha.chaoxing.com",
                        "Pragma" : 'no-cache',
            "Sec-Ch-Ua": '"Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"',
            'Sec-Ch-Ua-Mobile':'?0',
            'Sec-Ch-Ua-Platform':'"Linux"',
            'Sec-Fetch-Dest':'document',
            'Sec-Fetch-Mode':'navigate',
            'Sec-Fetch-Site':'none',
            'Sec-Fetch-User':'?1',
            'Upgrade-Insecure-Requests':'1',
            'User-Agent':'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        self.login_headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.05.2109131 MicroMessenger/8.0.5 Language/zh_CN webview/16364215743155638",
            "X-Requested-With": "XMLHttpRequest",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "Host": "passport2.chaoxing.com"
        }

        self.sleep_time = sleep_time
        self.max_attempt = max_attempt
        self.enable_slider = enable_slider
        self.reserve_next_day = reserve_next_day
        requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

    def _build_location_info(self, reservation):
        """
        构造位置信息字符串
        Args:
            reservation: 预约信息字典
        Returns:
            str: 完整的位置信息
        """
        return (reservation.get('firstLevelName', '') +
                reservation.get('secondLevelName', '') +
                reservation.get('thirdLevelName', '') +
                reservation.get('seatNum', ''))

    
    # login and page token
    def _get_page_token(self, url):
        response = self.requests.get(url=url, verify=False)
        logging.info(f"url: {url}")
        html = response.content.decode('utf-8')
        # print(html)
        token = re.findall(
            'token = \'(.*?)\'', html)[0] if len(re.findall('token = \'(.*?)\'', html)) > 0 else ""
        return token

    def _get_page_token_and_deptidenc(self, url):
        """
        获取页面token和deptIdEnc参数
        Args:
            url: 页面URL
        Returns:
            tuple: (token, deptIdEnc) 如果找不到则返回 ("", None)
        """
        response = self.requests.get(url=url, verify=False)
        logging.info(f"url: {url}")
        html = response.content.decode('utf-8')

        # 提取token
        token = re.findall(
            'token = \'(.*?)\'', html)[0] if len(re.findall('token = \'(.*?)\'', html)) > 0 else ""

        # 提取deptIdEnc - 尝试多种模式
        deptIdEnc = None
        deptidenc_patterns = [
            r'deptIdEnc["\']?\s*[:=]\s*["\']([^"\']+)["\']',  # deptIdEnc: "value" 或 deptIdEnc="value"
            r'deptIdEnc=([a-f0-9]+)',  # URL参数格式
            r'"deptIdEnc"\s*:\s*"([^"]+)"',  # JSON格式
            r'var\s+deptIdEnc\s*=\s*["\']([^"\']+)["\']',  # JavaScript变量
        ]

        for pattern in deptidenc_patterns:
            matches = re.findall(pattern, html, re.IGNORECASE)
            if matches:
                deptIdEnc = matches[0]
                logging.info(f"Found deptIdEnc using pattern '{pattern}': {deptIdEnc}")
                break

        if not deptIdEnc:
            logging.warning("Could not find deptIdEnc in HTML content")
            logging.debug(f"HTML preview: {html[:500]}...")

        return token, deptIdEnc

    def get_login_status(self):
        self.requests.headers = self.login_headers
        self.requests.get(url=self.login_page, verify=False)

    def login(self, username, password):
        username = AES_Encrypt(username)
        password = AES_Encrypt(password)
        parm = {
            "fid": -1,
            "uname": username,
            "password": password,
            "refer": "http%3A%2F%2Foffice.chaoxing.com%2Ffront%2Fthird%2Fapps%2Fseat%2Fcode%3Fid%3D4219%26seatNum%3D380",
            "t": True
        }
        jsons = self.requests.post(
            url=self.login_url, params=parm, verify=False)
        obj = jsons.json()
        if obj['status']:
            logging.info(f"User {username} login successfully")
            return (True, '')
        else:
            logging.info(f"User {username} login failed. Please check you password and username! ")
            return (False, obj['msg2'])

    # extra: get roomid
    def roomid(self, encode):
        url = f"https://office.chaoxing.com/data/apps/seat/room/list?cpage=1&pageSize=100&firstLevelName=&secondLevelName=&thirdLevelName=&deptIdEnc={encode}"
        json_data = self.requests.get(url=url).content.decode('utf-8')
        ori_data = json.loads(json_data)
        for i in ori_data["data"]["seatRoomList"]:
            info = f'{i["firstLevelName"]}-{i["secondLevelName"]}-{i["thirdLevelName"]} id为：{i["id"]}'
            print(info)

    # solve captcha 

    def resolve_captcha(self):
        logging.info(f"Start to resolve captcha token")
        captcha_token, bg, tp = self.get_slide_captcha_data()
        logging.info(f"Successfully get prepared captcha_token {captcha_token}")
        logging.info(f"Captcha Image URL-small {tp}, URL-big {bg}")
        x = self.x_distance(bg, tp)
        logging.info(f"Successfully calculate the captcha distance {x}")

        params = {
            "callback": "jQuery33109180509737430778_1716381333117",
            "captchaId": "42sxgHoTPTKbt0uZxPJ7ssOvtXr3ZgZ1",
            "type": "slide",
            "token": captcha_token,
            "textClickArr": json.dumps([{"x": x}]),
            "coordinate": json.dumps([]),
            "runEnv": "10",
            "version": "1.1.18",
            "_": int(time.time() * 1000)
        }
        response = self.requests.get(
            f'https://captcha.chaoxing.com/captcha/check/verification/result', params=params, headers=self.headers)
        text = response.text.replace('jQuery33109180509737430778_1716381333117(', "").replace(')', "")
        data = json.loads(text)
        logging.info(f"Successfully resolve the captcha token {data}")
        try: 
           validate_val = json.loads(data["extraData"])['validate']
           return validate_val
        except KeyError as e:
            logging.info("Can't load validate value. Maybe server return mistake.")
            return ""

    def get_slide_captcha_data(self):
        url = "https://captcha.chaoxing.com/captcha/get/verification/image"
        timestamp = int(time.time() * 1000)
        capture_key, token = generate_captcha_key(timestamp)
        referer = f"https://office.chaoxing.com/front/third/apps/seat/code?id=3993&seatNum=0199"
        params = {
            "callback": f"jQuery33107685004390294206_1716461324846",
            "captchaId": "42sxgHoTPTKbt0uZxPJ7ssOvtXr3ZgZ1",
            "type": "slide",
            "version": "1.1.18",
            "captchaKey": capture_key,
            "token": token,
            "referer": referer,
            "_": timestamp,
            "d": "a",
            "b": "a"
        }
        response = self.requests.get(url=url, params=params, headers=self.headers)
        content = response.text
        
        data = content.replace("jQuery33107685004390294206_1716461324846(",
                            ")").replace(")", "")
        data = json.loads(data)
        captcha_token = data["token"]
        bg = data["imageVerificationVo"]["shadeImage"]
        tp = data["imageVerificationVo"]["cutoutImage"]
        return captcha_token, bg, tp
    
    def x_distance(self, bg, tp):
        import numpy as np
        import cv2
        def cut_slide(slide):
            slider_array = np.frombuffer(slide, np.uint8)
            slider_image = cv2.imdecode(slider_array, cv2.IMREAD_UNCHANGED)
            slider_part = slider_image[:, :, :3]
            mask = slider_image[:, :, 3]
            mask[mask != 0] = 255
            x, y, w, h = cv2.boundingRect(mask)
            cropped_image = slider_part[y:y + h, x:x + w]
            return cropped_image
        c_captcha_headers = {
            "Referer": "https://office.chaoxing.com/",
            "Host": "captcha-b.chaoxing.com",
            "Pragma" : 'no-cache',
            "Sec-Ch-Ua": '"Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"',
            'Sec-Ch-Ua-Mobile':'?0',
            'Sec-Ch-Ua-Platform':'"Linux"',
            'Sec-Fetch-Dest':'document',
            'Sec-Fetch-Mode':'navigate',
            'Sec-Fetch-Site':'none',
            'Sec-Fetch-User':'?1',
            'Upgrade-Insecure-Requests':'1',
            'User-Agent':'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        bgc, tpc = self.requests.get(bg, headers=c_captcha_headers), self.requests.get(tp, headers=c_captcha_headers)
        bg, tp = bgc.content, tpc.content 
        bg_img = cv2.imdecode(np.frombuffer(bg, np.uint8), cv2.IMREAD_COLOR)  
        tp_img = cut_slide(tp)
        bg_edge = cv2.Canny(bg_img, 100, 200)
        tp_edge = cv2.Canny(tp_img, 100, 200)
        bg_pic = cv2.cvtColor(bg_edge, cv2.COLOR_GRAY2RGB)
        tp_pic = cv2.cvtColor(tp_edge, cv2.COLOR_GRAY2RGB)
        res = cv2.matchTemplate(bg_pic, tp_pic, cv2.TM_CCOEFF_NORMED)
        _, _, _, max_loc = cv2.minMaxLoc(res)  
        tl = max_loc
        return tl[0]

    def submit(self, times, roomid, seatid,seatId, action, submit_time=None):
        for seat in seatid:
            suc = False
            while ~suc and self.max_attempt > 0:
                token = self._get_page_token(self.url.format(roomid, seatId))
                logging.info(f"Get token: {token}")
                captcha = self.resolve_captcha() if self.enable_slider else "" 
                logging.info(f"Captcha token {captcha}")
               
                suc = self.get_submit(self.submit_url, times=times,token=token, roomid=roomid, seatid=seat,seatId=seatId, captcha=captcha, action=action, submit_time=submit_time)
                
                if suc:
                    return suc
                time.sleep(self.sleep_time)
                self.max_attempt -= 1
        return suc

    def sign(self, reservation_or_seatId=602):
        """
        签到功能，适用于旧版系统 (version=0)
        Args:
            reservation_or_seatId: 可以是预约对象(dict)或座位系统ID(int)
                - 如果是预约对象，直接对该预约进行签到
                - 如果是seatId，获取预约列表并选择可签到的预约
        Returns:
            str: 签到结果信息
        """
        try:
            # 判断参数类型
            if isinstance(reservation_or_seatId, dict):
                # 直接签到指定的预约
                target_reservation = reservation_or_seatId
                logging.info(f"Direct sign-in for reservation ID: {target_reservation.get('id')}")
            else:
                # 获取预约信息并选择可签到的预约
                seatId = reservation_or_seatId
                info = self.get_my_seat_id(seatId)
                if not info:
                    return "没有找到预约信息"

                # 筛选可签到的预约
                signable_reservations = []

                for reservation in info:
                    status = reservation.get('status')

                    # 检查是否已经签到
                    if status == self.SIGNED_STATUS:
                        location = self._build_location_info(reservation)
                        message = f"{location}: 已经签过到了，快学习吧~"
                        logging.info(message)
                        return message

                    # 可签到的状态：0-待履约, 3-暂离中, 5-被监督中
                    if status in self.SIGNABLE_STATUS:
                        signable_reservations.append(reservation)

                if not signable_reservations:
                    return "没有座位可以签到"

                # 选择要签到的预约（如果有多个，选择开始时间最早的）
                if len(signable_reservations) >= 2:
                    target_reservation = min(signable_reservations,
                                           key=lambda x: int(x.get('startTime', 0)))
                    logging.info(f"Found {len(signable_reservations)} signable reservations, selected earliest one")
                else:
                    target_reservation = signable_reservations[0]

            # 构造位置信息
            location = self._build_location_info(target_reservation)

            # 构造签到请求参数
            sign_data = {
                'id': target_reservation.get('id'),
                'seatId': target_reservation.get('seatId'),
                'seatNum': target_reservation.get('seatNum'),
                'roomId': target_reservation.get('roomId')
            }

            logging.info(f"Attempting to sign in at {location} with data: {sign_data}")

            # 发送签到请求 (使用旧版 seatengine 接口)
            response = self.requests.get(
                url='https://office.chaoxing.com/data/apps/seatengine/sign',
                params=sign_data
            )

            result = response.json()

            if result.get('success'):
                message = f"{location}: 签到成功"
                logging.info(message)
                return message
            else:
                error_msg = result.get('msg', '签到失败')
                message = f"{location}: {error_msg}"
                logging.warning(message)
                return message

        except Exception as e:
            error_message = f"签到过程中发生错误: {e}"
            logging.error(error_message)
            return error_message

    def sign_latest_reservation(self, seatId=602):
        """
        签到最早的可签到预约（修复后的逻辑）
        Args:
            seatId: 座位系统ID，默认602
        Returns:
            str: 签到结果信息
        """
        try:
            # 获取预约信息
            info = self.get_my_seat_id(seatId)
            if not info:
                return "没有找到预约信息"

            # 智能选择预约：优先选择当前时间最接近的可签到预约
            from datetime import datetime
            current_time = datetime.now()
            current_timestamp = int(current_time.timestamp() * 1000)

            # 筛选可签到的预约
            signable_reservations = []
            for reservation in info:
                status = reservation.get('status')
                if status in self.SIGNABLE_STATUS:  # 0-待履约, 3-暂离中, 5-被监督中
                    signable_reservations.append(reservation)

            if not signable_reservations:
                # 如果没有可签到的预约，检查是否已经签到
                for reservation in info:
                    if reservation.get('status') == self.SIGNED_STATUS:
                        location = self._build_location_info(reservation)
                        message = f"{location}: 已经签过到了，快学习吧~"
                        print(f"📋 检查结果: ✅ {message}")
                        return message
                return "没有可签到的预约"

            # 选择策略：
            # 1. 如果只有一个可签到预约，直接选择
            # 2. 如果有多个，选择开始时间最早的预约
            if len(signable_reservations) == 1:
                latest_reservation = signable_reservations[0]
                print(f"📋 预约选择: 找到唯一可签到预约")
            else:
                latest_reservation = min(signable_reservations, key=lambda x: int(x.get('startTime', 0)))
                print(f"📋 预约选择: 从 {len(signable_reservations)} 个可签到预约中选择最早的")

            # 显示预约信息
            status_name = {0: '待履约', 1: '学习中', 2: '已履约', 3: '暂离中', 5: '被监督中', 7: '已取消'}.get(latest_reservation.get('status'), '未知')
            type_name = {-1: '正常预约', 0: '待履约预约'}.get(latest_reservation.get('type'), '未知')
            location = self._build_location_info(latest_reservation)

            print(f"\n📋 选中的预约:")
            print(f"  预约ID: {latest_reservation.get('id')}")
            print(f"  类型: {type_name}")
            print(f"  状态: {status_name}")
            print(f"  座位: {latest_reservation.get('seatNum')}")
            print(f"  位置: {location}")

            # 转换时间戳显示时间
            start_time = latest_reservation.get('startTime', 0)
            if start_time:
                from datetime import datetime
                start_dt = datetime.fromtimestamp(start_time / 1000)
                print(f"  开始时间: {start_dt.strftime('%Y-%m-%d %H:%M:%S')}")

            # 检查是否可以签到
            status = latest_reservation.get('status')

            # 检查是否已经签到
            if status == self.SIGNED_STATUS:
                message = f"{location}: 已经签过到了，快学习吧~"
                print(f"  结果: ✅ {message}")
                logging.info(message)
                return message

            # 检查是否可签到
            if status not in self.SIGNABLE_STATUS:
                status_desc = {0: '待履约', 1: '学习中', 2: '已履约', 3: '暂离中', 5: '被监督中', 7: '已取消'}.get(status, '未知')
                message = f"{location}: 当前状态为'{status_desc}'，无法签到"
                print(f"  结果: ❌ {message}")
                return message

            print(f"  结果: 🔄 开始签到...")

            # 构造签到请求参数
            sign_data = {
                'id': latest_reservation.get('id'),
                'seatId': latest_reservation.get('seatId'),
                'seatNum': latest_reservation.get('seatNum'),
                'roomId': latest_reservation.get('roomId')
            }

            logging.info(f"Attempting to sign in at {location} with data: {sign_data}")

            # 发送签到请求 (使用旧版 seatengine 接口)
            response = self.requests.get(
                url='https://office.chaoxing.com/data/apps/seatengine/sign',
                params=sign_data
            )

            result = response.json()

            if result.get('success'):
                message = f"{location}: 签到成功"
                print(f"  最终结果: ✅ {message}")
                logging.info(message)
                return message
            else:
                error_msg = result.get('msg', '签到失败')
                message = f"{location}: {error_msg}"
                print(f"  最终结果: ❌ {message}")
                logging.warning(message)
                return message

        except Exception as e:
            error_message = f"签到过程中发生错误: {e}"
            print(f"  最终结果: ❌ {error_message}")
            logging.error(error_message)
            return error_message
    


    # 获取预约列表
    def get_my_seat_id(self, seatId=602):
        """
        获取预约列表，使用旧版接口
        Args:
            seatId: 座位系统ID，默认602
        Returns:
            list: 今天和明天的有效预约列表
        """
        try:
            logging.debug(f"Getting reservation list for seatId={seatId}")

            # 使用旧版API接口，使用固定的seatIdEnc参数
            seatIdEnc = "9e3e9d2f2ef09ac1"  # 固定值，适用于大多数情况
            url = f'https://office.chaoxing.com/data/apps/seatengine/reservelist?cpage=1&pageSize=10&type=-1&seatId={seatId}&seatIdEnc={seatIdEnc}&showQrCode=1'
            logging.debug(f"API URL: {url}")

            response = self.requests.get(url=url).json()
            logging.debug(f"API Response for seatId {seatId}: {response}")

            if 'data' not in response or 'reserveList' not in response['data']:
                logging.debug(f"API failed for seatId {seatId}")
                logging.debug(f"Response structure: {list(response.keys()) if response else 'None'}")
                return []

            reserveList = response['data']['reserveList']
            logging.debug(f"API found {len(reserveList)} total reservations")

            result = []
            # 筛选今天和明天的有效预约
            for reservation in reserveList:
                # 筛选条件：type=-1或type=0都是有效预约
                if reservation['type'] in [-1, 0]:  # -1=正常预约, 0=待履约预约
                    if reservation['today'] == self.today or reservation['today'] == self.tomorrow:
                        result.append(reservation)
                        logging.debug(f"Added valid reservation: type={reservation['type']}, today={reservation['today']}, status={reservation['status']}")

            logging.debug(f"Found {len(result)} valid reservations (today: {self.today}, tomorrow: {self.tomorrow})")
            return result

        except Exception as e:
            logging.error(f"Failed to get reservation list: {e}")
            return []

    def get_submit(self, url, times, token, roomid, seatid,seatId, captcha="", action=False, submit_time=None):
        delta_day = 1 if self.reserve_next_day else 0
        day = datetime.datetime.now().date() + datetime.timedelta(days=0+delta_day)  # 预约今天，修改days=1表示预约明天
        day = day.strftime("%Y-%m-%d")  # 格式化日期为YYYY-MM-DD格式
        if action:
            day = datetime.datetime.now().date() + datetime.timedelta(days=1+delta_day)  # 由于action时区问题导致其早+8区一天
        parm = {
            "roomId": roomid,
            "startTime": times[0],
            "endTime": times[1],
            "day": str(day),
            "seatNum": seatid,
            "captcha": captcha,
            "token": token,
           
        }
        
        enc = check.enc(parm)
        parm["enc"] = enc
        
        # 构建完整的URL和参数
        full_url = f"https://reserve.chaoxing.com/data/apps/seatengine/submit?roomId={roomid}&startTime={times[0]}&endTime={times[1]}&day={str(day)}&captcha={captcha}&seatNum={seatid}&token={token}&enc={enc}&seatId={seatId}"
        logging.info(f"完整请求URL: {full_url}")
        logging.info(f"提交参数: {parm}")

        # 如果指定了提交时间，等待直到指定时间
        if submit_time:
            logging.info(f"等待直到指定时间 {submit_time} 再进行提交...")
            wait_until_specific_time(submit_time)
            logging.info("开始提交预约请求...")
        logging.info("开始发送请求")
        time.sleep(0.4)
        html = self.requests.post(
            url=url, params=parm, verify=True).content.decode('utf-8')
        logging.info("已发送")
        self.submit_msg.append(
            times[0] + "~" + times[1] + ':  ' + str(json.loads(html)))
        logging.info(json.loads(html))
        return json.loads(html)["success"]
