<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width,initial-scale=1,user-scalable=0">
  <meta name="format-detection" content="telephone=no">
  <title>预约记录</title>
<base href="https://office.chaoxing.com/">
<script type="text/javascript">
  var photoUrl = 'http://photo.chaoxing.com'
  var panUrl = 'http://pan-yz.chaoxing.com'
  var noteUrl = 'http://noteyd.chaoxing.com'
  var staticDomain = 'https://office-static.chaoxing.com/r/'
  photoUrl = document.location.protocol + photoUrl.split(":")[1]
  panUrl = document.location.protocol + panUrl.split(":")[1]
  noteUrl = document.location.protocol + noteUrl.split(":")[1]
  if (staticDomain !== '') {
    staticDomain = document.location.protocol + staticDomain.split(":")[1]
  }
</script>

<link rel="stylesheet" href="https://office-static.chaoxing.com/r/staticreserve/style/reset.css">
<link rel="stylesheet" href="https://office-static.chaoxing.com/r/staticreserve/style/apps/seatengine/seat.css?t=2025070220">
<link rel="stylesheet" href="https://office-static.chaoxing.com/r/staticreserve/style/apps/reserve/iphonex.css">
<link rel="stylesheet" href="https://office-static.chaoxing.com/r/staticreserve/style/apps/reserve/reserve_mobile.css">
<script src="https://office-static.chaoxing.com/r/staticreserve/js/lib/jquery/jquery.min.js"></script>
<script>document.write("<script src='https://captcha.chaoxing.com/load-d.min.js?t=" + parseInt(Date.now()/60000) + "'><\/script>");</script>
<script>document.write("<script src='https://captcha.chaoxing.com/light.js?t=" + parseInt(Date.now()/60000) + "'><\/script>");</script>
<script type="text/javascript" src="//api.map.baidu.com/api?type=webgl&v=1.0&ak=p7HrxjMD6R1hbPFP9sNgkfHjsNZumBai"></script>
<script>document.write("<script src='https://cstaticdun.126.net/load.min.js?t=" + parseInt(Date.now()/60000) + "'><\/script>");</script>
<script type="text/javascript" src="https://office-static.chaoxing.com/r/staticreserve/js/lib/requirejs/require.min.js"></script>
<script type="text/javascript" src="https://office-static.chaoxing.com/r/staticreserve/js/src/front/apps/seatengine/main.js?t=2025070220"></script>
<script>requirejs.config({urlArgs: 't=2025070220'})</script>
</head>
<body>
<div class="record" v-cloak>
  <ul class="rec_top">
    <li class="rec_tab" :class="{active:index == -1}" @click="changeType(-1)"><span>全部</span></li>
    <li class="rec_tab" :class="{active:index == 0}" @click="changeType(0)"><span>{{seatConfig.appType === 0 ? '待履约' : '已预约'}}</span></li>
    <li class="rec_tab" :class="{active:index == 2}" @click="changeType(2)"><span>已履约</span></li>
    <li class="rec_tab" :class="{active:index == 7}" @click="changeType(7)"><span>已取消</span></li>
    <li class="rec_tab" v-if="seatConfig.appType === 0" :class="{active:index == 8}" @click="changeType(8)"><span>违约</span></li>
  </ul>
  <p class="gnorecord" v-if="isNotExistData">无预约记录</p>
  <div class="rec_cont" v-if="seatConfig.appType === 0">
    <div class="rec_per" :class="aprvStatusCls(data.status,data.type)" v-for="(data,index) in reserveList">
      <h6 class="rec_title">{{data.startTime |format('HH:mm')}}-{{data.endTime | format('HH:mm')}}<span>{{getStatusName(data.status,data.type)}}</span></h6>
      <p class="rec_addr owt1">
        {{data.firstLevelName}}<span v-if="data.secondLevelName">-{{data.secondLevelName}}</span><span v-if="data.thirdLevelName">-{{data.thirdLevelName}}</span><span class="rec_num">座位<em>{{data.seatNum}}</em></span>
      </p>
      <p class="rec_tip">{{data.startTime |format('YYYY.MM.DD')}}<span>{{getWeekStr(data.startTime)}}</span></p>
      <ol class="rec_label">
        <li class="rec_btn" @click="getViolationDetail(data.id)" v-if="data.type != -1">详情</li>
        <li class="rec_btn" @click="showQrCode(data)" v-if="data.showQrCode == 1">开锁码</li>
        <li class="rec_btn" @click="reReserve(data.roomId,data.seatNum)" v-if="data.status == 2 && !znmzApp && !wclg && !hbsm">再次预约</li>
      </ol>
    </div>
    
    <div></div>
  </div>
  <div class="rec_cont" v-else>
    <div class="rec_per" :class="aprvStatusCls(data.status,data.type)" v-for="(data,index) in reserveList">
      <h6 class="rec_title" style="font-size: .4rem">{{data.startDay}} ~ {{data.endDay}}<span>{{getStatusName(data.status,data.type)}}</span></h6>
      <p class="rec_addr owt1">
        {{data.firstLevelName}}<span v-if="data.secondLevelName">-{{data.secondLevelName}}</span><span v-if="data.thirdLevelName">-{{data.thirdLevelName}}</span><span
          class="rec_num">{{seatConfig.type}}<em>{{data.seatNum}}</em></span>
      </p>
      <p class="rec_tip"></p>
      <ol class="rec_label">
        <li class="rec_btn" @click="getReserveLogsInfo(data.id)" v-if="data.status !== 7">使用详情</li>
        <li class="rec_btn" @click="showQrCode(data)" v-if="data.showQrCode == 1">开锁码</li>
        <li class="rec_btn" @click="reReserve(data.roomId,data.seatNum)" v-if="data.status === 2 && !znmzApp && !wclg && !hbsm">再次预约</li>
        <li class="rec_btn rec_qd" @click="askSignOut(data)" v-if="data.status == 1 && seatConfig.closeSignBack == 0">签退</li>
        <li class="rec_btn rec_qd" @click="askSignBack(data)" v-if="data.status == 9 && seatConfig.closeSignBack == 0">
          <span style="color: red">{{seatConfig.type=='座位' ? '退座' : '退订'}}</span>
        </li>
        <li class="rec_btn rec_qd" @click="askCancel(data.id)" v-if="data.status == 0 && seatConfig.closeSignBack == 0">取消</li>
      </ol>
    </div>
    
    <div></div>
  </div>
  
  <div class="screenMask" v-if="cancelShow || signOutShow || signBackShow || unlockCodeDialog.show"></div>
  <div class="quit_pop leave_pop" v-if="cancelShow">
    <div class="quit_top">确定要取消此次预约吗？</div>
    <div class="quit_btm">
      <span @click="cancel(false)">取消</span>
      <span class="quit_sure" @click="cancel(true)">确认</span>
    </div>
  </div>
  <div class="quit_pop leave_pop" v-if="signOutShow">
    <div class="quit_top"><center>确认进行签退操作吗？</center></div>
    <div class="quit_btm">
      <span @click="signOut(false)">取消</span>
      <span class="quit_sure" @click="signOut(true)">确认</span>
    </div>
  </div>
  <div class="quit_pop leave_pop" v-if="signBackShow">
    <div class="quit_top">退订后{{seatConfig.type}}将被释放，确认结束本次预约吗？</div>
    <div class="quit_btm">
      <span @click="signBack(false)">取消</span>
      <span class="quit_sure" @click="signBack(true)">确认</span>
    </div>
  </div>
  <div class="res_mask" v-if="viShow"></div>
  
  
  <div class="break_pop one" v-if="viLog.type == 1 && viShow">
    <div class="break_top">
      <ul class="break_ul">
        <li class="break_lis">
          <span class="break_lis_left">违约原因</span>
          <span class="break_lis_right">使用完毕未退座</span>
        </li>
        <li class="break_lis">
          <span class="break_lis_left">应退座时间</span>
          <span class="break_lis_right yellow">{{viLog.inserttime | format('HH:mm')}} 前进行退座</span>
        </li>
      </ul>
      <p class="break_info">使用完成后，要进行退座哦！</p>
    </div>
    <div class="break_btn" @click="know">我知道了</div>
  </div>
  
  <div class="break_pop one" v-if="viLog.type == 0 && viShow">
    <div class="break_top">
      <ul class="break_ul">
        <li class="break_lis">
          <span class="break_lis_left">违约原因</span>
          <span class="break_lis_right">未在规定时间内签到</span>
        </li>
        <li class="break_lis">
          <span class="break_lis_left">应签到时间</span>
          <span class="break_lis_right yellow">{{viLog.inserttime | format('HH:mm')}} 前进行签到</span>
        </li>
      </ul>
      <p class="break_info">预约完成后，要在规定时间内签到哦！</p>
    </div>
    <div class="break_btn" @click="know">我知道了</div>
  </div>
  
  <div class="break_pop" v-if="viLog.type == 5 && viShow">
    <div class="break_top">
      <ul class="break_ul">
        <li class="break_lis">
          <span class="break_lis_left">违约原因</span>
          <span class="break_lis_right">被监督后，未在规定时间内落座</span>
        </li>
        <li class="break_lis">
          <span class="break_lis_left">被监督开始时间</span>
          <span class="break_lis_right yellow">{{reLog.inserttime | format('HH:mm:ss')}}</span>
        </li>
        <li class="break_lis">
          <span class="break_lis_left">应落座时间</span>
          <span class="break_lis_right yellow">{{viLog.inserttime | format('HH:mm:ss')}} 前返回落座</span>
        </li>
      </ul>
      <p class="break_info">被监督时，要在规定时间内回到座位哦！</p>
    </div>
    <div class="break_btn" @click="know">我知道了</div>
  </div>
  
  <div class="break_pop" v-if="viLog.type == 3 && viShow">
    <div class="break_top">
      <ul class="break_ul">
        <li class="break_lis">
          <span class="break_lis_left">违约原因</span>
          <span class="break_lis_right">暂离后未在规定时间内返回</span>
        </li>
        <li class="break_lis">
          <span class="break_lis_left">暂离开始时间</span>
          <span class="break_lis_right yellow">{{reLog.inserttime | format('HH:mm:ss')}}</span>
        </li>
        <li class="break_lis">
          <span class="break_lis_left">应返回时间</span>
          <span class="break_lis_right yellow">{{viLog.inserttime | format('HH:mm:ss')}} 前返回落座</span>
        </li>
      </ul>
      <p class="break_info">暂离后，要在规定时间内返回扫码落座哦！</p>
    </div>
    <div class="break_btn" @click="know">我知道了</div>
  </div>
  
  <div class="pop_wrap" v-if="unlockCodeDialog.show">
    <div class="code_pop">
      
      <div class="code_pic" >
        <img v-if="unlockCodeDialog.imgShow" :src="unlockCodeDialog.url" alt="">
        <span>请将二维码对准扫码设备扫描</span>
      </div>
      <div class="pop_btm" @click="location.reload()">关闭</div>
    </div>
  </div>
</div>
<script>
  require(['jquery', 'Vue', 'appUtils', 'moment', 'popup', 'dropload'], function ($, Vue, AppUtils, moment, popup) {
    'use strict'
    var serverNow = new Date($.ajax({async: false}).getResponseHeader('Date')),
        seatId = '602',
        deptIdEnc = '991fe2698ebc49b9'
    function operateData(url, data) {
      return $.getJSON(url, data)
    }
    new Vue({
      el: '.record',
      data: {
        seatConfig: {},
        reserveList: [],
        isNotExistData: false,
        index: -1,
        droploader: {
          dropload: null,//翻页加载条
          url: '',//翻页请求的地址
          params: {//翻页请求的参数
            cpage: 1,
            pageSize: 10,
            type: -1,
            seatId: seatId,
            seatIdEnc: '9e3e9d2f2ef09ac1',
            showQrCode: 1
          }
        },
        unlockCodeDialog: {
          show: false,
          url: '',
          imgShow: false
        },
        cancelShow: false,
        signOutShow: false,
        signBackShow: false,
        itemId: '',
        viLog: '',//违约记录
        reLog: '',//预约最新操作记录
        viShow: false,
        znmzApp: false, //中南名族大学定制
        wclg: false, //武昌理工学院定制
        hbsm: false //湖北商贸学院定制
      },
      methods: {
        changeType: function (type) {
          var _this = this
          _this.droploader.params.showQrCode = 1
          _this.isNotExistData = false
          _this.droploader.params['type'] = type
          _this.droploader.url = 'data/apps/seatengine/reservelist'
          _this.index = type
          _this.reserveList = []
          _this.droploader.params.cpage = 1
          $('.isbottom').hide()
          this.$nextTick(function () {
            _this.droploader.dropload.unlock()
            _this.droploader.dropload.noData(false)
            _this.droploader.dropload.resetload()
          })
        },
        askCancel: function (id) {
          var _this = this
          _this.itemId = id
          _this.cancelShow = true
        },
        cancel: function (flag) {
          var _this = this
          if (flag) {
            operateData('data/apps/seatengine/cancel', {id: _this.itemId}).then(function (data) {
              if (data.success) {
                AppUtils.clientMessageDisplay('取消成功')
                _this.changeType(_this.index)
              }
            })
          }
          _this.itemId = ''
          _this.cancelShow = false
        },
        askSignOut: function (data) {
          var _this = this
          _this.signOutShow = true
          _this.itemId = data.id
        },
        signOut: function (flag) {
          var _this = this
          if (flag) {
            operateData('data/apps/seatengine/signlongback', {id: _this.itemId}).then(function (json) {
              if (json.success) {
                AppUtils.clientMessageDisplay('签退成功')
              } else {
                AppUtils.clientMessageDisplay(json.msg)
              }
              location.reload()
            })
          }
          _this.itemId = ''
          _this.signOutShow = false
        },
        askSignBack: function (data) {
          var _this = this
          _this.signBackShow = true
          _this.itemId = data.id
        },
        signBack: function (flag) {
          var _this = this
          if (flag) {
            operateData('data/apps/seatengine/signlongout', {id: _this.itemId}).then(function (json) {
              if (json.success) {
                AppUtils.clientMessageDisplay('退座成功')
              } else {
                AppUtils.clientMessageDisplay(json.msg)
              }
              location.reload()
            })
          }
          _this.itemId = ''
          _this.signBackShow = false
        },
        getViolationDetail: function (id) {
          var _this = this
          operateData('data/apps/seatengine/violation', {reserveId: id}).then(function (json) {
            if (json.success && json.data.viLog) {
              _this.viShow = true
              _this.viLog = json.data.viLog
              _this.reLog = json.data.reLog
            } else {
              popup.alert('未找到对应记录')
            }
          })
        },
        getReserveLogsInfo: function (id) {
          location.href = location.origin + '' + '/front/third/apps/seatengine/reservelogs?id=' + id + '&seatId=' + seatId
        },
        know: function () {
          var _this = this
          _this.viShow = false
          _this.viShow = ''
          _this.reLog = ''
        },
        reReserve: function (roomId, seatNum) {
          //判断该区域是否可以预约
          operateData('data/apps/seatengine/room/info', {id: roomId}).then(function (json) {
            if (json.success && json.data.seatRoom) {
              var seatConfig = json.data.seatConfig
              var serverTime = serverNow.getHours() + serverNow.getMinutes() / 60
              var configTime = parseInt(seatConfig.reserveBeforeTime.split(':')[0]) + parseInt(seatConfig.reserveBeforeTime.split(':')[1]) / 60
              //假如设置提前预约日期为当天，且当前时间小于提前预约时间，则无法进入快速选座界面
              if (seatConfig.reserveBeforeDay === 0 && serverTime < configTime) {
                popup.alert(seatConfig.reserveBeforeTime + '后可预约')
                return
              }
              var seatRoom = json.data.seatRoom
              if (seatRoom.status == 1) {
                popup.alert('该区域已暂停预约')
                return
              }
              if (seatRoom.isShow == 1) {
                popup.alert('该区域仅支持现场预约')
                return
              }
              if (seatRoom.isOpen == 1) {
                popup.alert('该区域已暂停预约')
                return
              }
              if (!seatRoom.roleShow) {
                popup.alert('该区域仅面向部分人员开放')
                return
              }
              if (seatRoom.startSeatNum > parseInt(seatNum)) {
                popup.alert('该座位已被移除')
                return
              }
              var seatAttributes = json.data.seatAttributes
              for (var i = 0; i < seatAttributes.length; i++) {
                var seat = seatAttributes[i]
                if (seat.isReserve == 0 && seatNum == seat.seatNum) {
                  popup.alert('该座位已暂停预约')
                  return
                }
              }
              location.href = location.origin + '' + '/front/third/apps/seatengine/select?id=' + roomId + '&day=' + moment(new Date())
                  .format('YYYY-MM-DD') + '&seatNum=' + seatNum + '&backLevel=2' + '&seatId=' + seatId + '&fidEnc=' + deptIdEnc
            } else {
              popup.alert(json.msg)
            }
          })
        },
        aprvStatusCls: function (status, type) {
          if (type === -1) {
            switch (status) {
              case 0:
                return 'wait'
              case 1:
              case 9:
                return 'now'
              case 2:
                return 'already'
              case 3:
                return 'now'
              case 5:
                return 'now'
              case 7:
                return 'cancel'
            }
          } else {
            return 'break'
          }
        },
        getStatusName: function (status, type) {
          if (type === -1) {
            switch (status) {
              case 0:
              case 9:
                if (this.seatConfig.appType === 1) {
                  return '已预约'
                }
                return '待履约'
              case 1:
                if (this.seatConfig.appType === 1) {
                  return '使用中'
                }
                return '学习中'
              case 2:
                return '已履约'
              case 3:
                return '暂离中'
              case 5:
                return '被监督中'
              case 7:
                return '已取消'
            }
          } else {
            return '违约'
          }
        },
        getWeekStr: function (time) {
          var day = new Date(time).getDay()
          switch (day) {
            case 0:
              return '星期日'
            case 1:
              return '星期一'
            case 2:
              return '星期二'
            case 3:
              return '星期三'
            case 4:
              return '星期四'
            case 5:
              return '星期五'
            case 6:
              return '星期六'
          }
        },
        showQrCode: function (data) {
          var _this = this
          operateData('data/apps/seatengine/unlock/qrcode', {reserveId: data.id}).then(function (json) {
            if (!json.success) {
              popup.alert(json.msg)
              return
            }
            _this.unlockCodeDialog.url = json.data.url
            _this.unlockCodeDialog.imgShow = true
            _this.unlockCodeDialog.show = true
          })
        }
      },
      filters: {
        format: function (value, formater) {
          return moment(value).format(formater)
        }
      },
      mounted: function () {
        var _this = this, totalPage = 0
        operateData('data/apps/seatengine/config', {seatId: seatId, seatIdEnc: '9e3e9d2f2ef09ac1'}).then(function (json) {
          if (json.success) {
            _this.seatConfig = json.data.seatConfig
            _this.droploader.url = 'data/apps/seatengine/reservelist'
            _this.droploader.dropload = $('.rec_cont').dropload({
              scrollArea: $('.rec_cont'),
              threshold: 100,
              domDown: {
                domClass: 'isbottom',
                domNoData: '已经到底啦~(&gt;_&lt;)~~'
              },
              loadDownFn: function (me) {
                operateData(_this.droploader.url, _this.droploader.params).then(function (data) {
                  if (data.success) {
                    window.document.title = _this.seatConfig.type + '预约'
                    _this.znmzApp = _this.seatConfig.id === 1234
                    _this.wclg = _this.seatConfig.deptId === 1752 && (_this.seatConfig.id === 2273 || _this.seatConfig.id === 2232)
                    _this.hbsm = _this.seatConfig.id === 1309
                    totalPage = data.data.totalPage
                    if (data.data.totalRow === 0) {
                      _this.isNotExistData = true
                    }
                    _this.droploader.params.showQrCode = data.data.showQrCode
                    _this.reserveList = _this.reserveList.concat(data.data.reserveList)
                    if (totalPage === 1) {
                      $('.isbottom').hide()
                    }
                    _this.droploader.params['cpage']++
                    _this.$nextTick(function () {
                      if (totalPage !== 0 && _this.droploader.params['cpage'] > totalPage) {
                        me.lock() // 锁定
                        me.noData() // 无数据
                      }
                      me.delayResetload()
                    })
                  }
                })
              }
            })
          }
        })
        $('body').dropload({
          scrollArea: $('.rec_cont'),
          loadUpFn: function (me) {
            _this.changeType(_this.index)
            me.resetload()
          }
        })
      }
    })
  })
</script>
<script type="text/javascript">
  require(['lever', 'ratio'], function (AlloyLever) {
    'use strict'
    AlloyLever.config({cdn: 'https://office-static.chaoxing.com/r/staticreserve/js/lib/lever/vconsole.min.js'})
  })
</script>

</body>
</html>
