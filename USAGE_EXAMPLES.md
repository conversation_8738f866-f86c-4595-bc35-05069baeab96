# 使用示例和最佳实践

## 📋 配置文件示例

### 基础配置 (config2.json)
```json
{
  "reserve": [
    {
      "username": "18878578978",
      "password": "your_password",
      "seatId": "602",
      "comment": "连云港市图书馆-用户1"
    },
    {
      "username": "18936616637", 
      "password": "your_password",
      "seatId": "2793",
      "comment": "连云港市图书馆-用户2"
    }
  ]
}
```

### 完整配置示例
```json
{
  "reserve": [
    {
      "username": "your_username",
      "password": "your_password",
      "seatId": "602",
      "comment": "主账号",
      "time": ["09:00", "14:00"],
      "roomid": "866",
      "seatid": "114",
      "daysofweek": [1, 2, 3, 4, 5],
      "target_time": "09:00"
    }
  ]
}
```

## 🚀 运行示例

### 1. 基础签到
```bash
# 进入项目目录
cd xuexitong_old

# 运行主程序（使用默认配置）
python main.py

# 预期输出：
# 🎯 用户: 连云港市图书馆-用户1 - 开始签到最近一次预约...
# 📋 最近一次预约:
#   预约ID: 34730923
#   类型: 正常预约
#   状态: 学习中
#   座位: 114
#   位置: 南区分馆四楼114
#   开始时间: 2025-07-06 12:00:00
#   结果: ✅ 南区分馆四楼114: 已经签过到了，快学习吧~
```

### 2. 调试模式运行
```python
# 创建调试脚本 debug_run.py
import logging
import json
from main import get_sign

# 启用详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# 读取配置
with open("config2.json", "r", encoding='utf-8') as f:
    config = json.load(f)

# 转换配置格式
users = []
for user in config["reserve"]:
    main_user = {
        "_comment": user.get("comment", "未知用户"),
        "username": user["username"],
        "password": user["password"],
        "time": user.get("time", []),
        "roomid": user.get("roomid", ""),
        "seatid": user.get("seatid", ""),
        "daysofweek": user.get("daysofweek", []),
        "seatId": user.get("seatId", "602")
    }
    users.append(main_user)

# 执行签到
get_sign(users, action=False)
```

### 3. 单独测试预约功能
```python
# 创建测试脚本 test_reservation.py
from utils import reserve

# 创建实例
s = reserve(sleep_time=0.1, max_attempt=3, enable_slider=False)

# 登录
login_success, msg = s.login(username="your_username", password="your_password")
if login_success:
    print("✅ 登录成功")
    
    # 设置请求头
    api_headers = {
        'Host': 'office.chaoxing.com',
        'Referer': 'https://office.chaoxing.com/',
        'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'X-Requested-With': 'XMLHttpRequest'
    }
    s.requests.headers.update(api_headers)
    
    # 获取预约信息
    result = s.get_my_seat_id(seatId=602)
    print(f"预约信息: {result}")
    
    # 签到最近一次预约
    sign_result = s.sign_latest_reservation(seatId=602)
    print(f"签到结果: {sign_result}")
else:
    print(f"❌ 登录失败: {msg}")
```

## 📊 输出示例

### 成功签到输出
```
🎯 用户: 连云港市图书馆-用户1 - 开始签到最近一次预约...

📋 最近一次预约:
  预约ID: 34730923
  类型: 正常预约
  状态: 学习中
  座位: 114
  位置: 南区分馆四楼114
  开始时间: 2025-07-06 12:00:00
  结果: ✅ 南区分馆四楼114: 已经签过到了，快学习吧~

最终签到结果: 南区分馆四楼114: 已经签过到了，快学习吧~
============================================================
```

### 无预约信息输出
```
🎯 用户: 连云港市图书馆-用户2 - 开始签到最近一次预约...

最终签到结果: 没有找到预约信息
============================================================
```

### 需要签到输出
```
📋 最近一次预约:
  预约ID: 34730924
  类型: 正常预约
  状态: 待履约
  座位: 115
  位置: 南区分馆四楼115
  开始时间: 2025-07-06 14:00:00
  结果: 🔄 开始签到...
  签到状态: ✅ 签到成功！
```

## 🔧 常用操作

### 1. 批量账号管理
```python
# 批量添加账号到配置文件
import json

# 读取现有配置
with open("config2.json", "r", encoding='utf-8') as f:
    config = json.load(f)

# 添加新账号
new_users = [
    {
        "username": "new_user1",
        "password": "password1",
        "seatId": "603",
        "comment": "新用户1"
    },
    {
        "username": "new_user2", 
        "password": "password2",
        "seatId": "604",
        "comment": "新用户2"
    }
]

config["reserve"].extend(new_users)

# 保存配置
with open("config2.json", "w", encoding='utf-8') as f:
    json.dump(config, f, ensure_ascii=False, indent=2)
```

### 2. 定时任务设置
```bash
# Linux/Mac 使用 crontab
# 每天上午8:30和下午1:30执行签到
30 8,13 * * * cd /path/to/xuexitong_old && python main.py

# Windows 使用任务计划程序
# 或者创建批处理文件 auto_sign.bat:
@echo off
cd /d "C:\path\to\xuexitong_old"
python main.py
pause
```

### 3. 日志分析
```python
# 分析日志文件
import re
from datetime import datetime

def analyze_logs(log_file):
    with open(log_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 统计签到成功次数
    success_count = len(re.findall(r'已经签过到了|签到成功', content))
    
    # 统计登录失败次数
    login_fail_count = len(re.findall(r'Login failed', content))
    
    # 统计API调用次数
    api_calls = len(re.findall(r'GET /data/apps/', content))
    
    print(f"签到成功次数: {success_count}")
    print(f"登录失败次数: {login_fail_count}")
    print(f"API调用次数: {api_calls}")

# 使用示例
analyze_logs("logs/config2_20250706.log")
```

## 🐛 故障排除示例

### 1. 网络连接问题
```python
# 测试网络连接
import requests

def test_connection():
    try:
        response = requests.get("https://office.chaoxing.com", timeout=10)
        print(f"✅ 网络连接正常，状态码: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ 网络连接失败: {e}")
        return False

if test_connection():
    # 继续执行签到
    pass
else:
    print("请检查网络连接后重试")
```

### 2. 配置文件验证
```python
# 验证配置文件格式
import json

def validate_config(config_file):
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        if "reserve" not in config:
            print("❌ 配置文件缺少 'reserve' 字段")
            return False
        
        for i, user in enumerate(config["reserve"]):
            required_fields = ["username", "password", "seatId"]
            for field in required_fields:
                if field not in user:
                    print(f"❌ 用户 {i+1} 缺少必需字段: {field}")
                    return False
        
        print("✅ 配置文件格式正确")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

# 使用示例
validate_config("config2.json")
```

## 📈 性能优化建议

1. **请求间隔**：适当增加 `SLEEPTIME` 避免频繁请求
2. **并发控制**：避免同时运行多个实例
3. **日志管理**：定期清理旧日志文件
4. **错误重试**：合理设置 `MAX_ATTEMPT` 参数
5. **网络优化**：使用稳定的网络环境

---

**提示**：以上示例仅供参考，请根据实际情况调整参数和配置。
